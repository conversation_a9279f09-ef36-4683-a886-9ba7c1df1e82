import { create } from 'zustand';
import { UserData } from '@/app/auth/userStorage';
import {}

interface UserStore {
  userData: UserData | null;
  setUserData: (data: UserData) => void;
  fetchUserData: () => Promise<void>;
  clearUserData: () => void;
}

export const useUserStore = create<UserStore>((set) => ({
  userData: null,
  setUserData: (data) => set({ userData: data }),
  fetchUserData: async () => {
    const userData = await getUserData();
    set({ userData });
  },
  clearUserData: () => set({ userData: null }),
}));

const getUserData = async () => {
  const userData = await userStorage.getUser();
  return userData;
};
